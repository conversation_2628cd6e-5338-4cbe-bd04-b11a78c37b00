"use client"

import { Grad<PERSON><PERSON>ard, GradientCardContent } from "@/components/ui/gradient-card"
import CountUp from "@/components/animations/count-up"
import ScrollFloat from "@/components/animations/scroll-float"
import { WebflowFadeIn, WebflowStagger, WebflowCounter, WebflowMorphBackground } from "@/components/animations/WebflowAnimations"
import { motion } from "framer-motion"

export default function Statistics() {
  return (
    <section className="relative py-24 lg:py-32 overflow-hidden">
      {/* Enhanced background effects with Webflow styling */}
      <WebflowMorphBackground className="opacity-30" />
      <div className="absolute inset-0 bg-gradient-to-b from-blue-500/5 via-purple-500/5 to-emerald-500/5" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(59,130,246,0.1)_0%,transparent_50%)]" />

      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <div className="flex flex-col items-center justify-center space-y-12 text-center">
          <WebflowFadeIn delay={0.2} direction="up" className="space-y-6 max-w-4xl">
            <motion.div
              className="inline-flex items-center rounded-full bg-gradient-to-r from-amber-500/20 via-orange-500/20 to-yellow-500/20 backdrop-blur-xl border border-amber-500/30 px-6 py-3 text-sm text-amber-300"
              whileHover={{
                scale: 1.05,
                boxShadow: '0 8px 32px rgba(245, 158, 11, 0.3)',
                borderColor: 'rgba(245, 158, 11, 0.5)'
              }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <span className="mr-2">📊</span>
              Proven Results
            </motion.div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight leading-tight text-white">
              Trusted by developers <span className="gradient-text-webflow">WORLDWIDE</span>
            </h2>

            <p className="mx-auto max-w-3xl text-xl md:text-2xl text-slate-300 leading-relaxed">
              Join thousands of successful developers who are building profitable SaaS applications with our premium
              platform.
            </p>
          </WebflowFadeIn>

          <div className="w-full pt-12">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
              {statistics.map((stat, index) => (
                <WebflowFadeIn key={stat.label} delay={0.4 + index * 0.1} direction="up">
                  <motion.div
                    className="card-webflow group transition-all duration-500"
                    whileHover={{
                      scale: 1.05,
                      boxShadow: '0 25px 50px rgba(16, 185, 129, 0.2)',
                    }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  >
                    <GradientCardContent className="flex flex-col items-center justify-center p-8 text-center">
                      <div className="text-4xl sm:text-5xl lg:text-6xl font-bold gradient-text-webflow mb-3">
                        <WebflowCounter
                          from={0}
                          to={stat.value}
                          duration={2}
                          suffix={stat.suffix}
                        />
                      </div>
                      <p className="text-sm sm:text-base text-slate-300 font-medium">{stat.label}</p>
                      <motion.div
                        className="mt-2 h-1 w-12 bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-500 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"
                        initial={{ width: 0 }}
                        whileInView={{ width: 48 }}
                        transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
                      />
                    </GradientCardContent>
                  </motion.div>
                </WebflowFadeIn>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

const statistics = [
  {
    value: 5000,
    suffix: "+",
    label: "Projects Analyzed",
  },
  {
    value: 87,
    suffix: "%",
    label: "Success Rate",
  },
  {
    value: 3200,
    suffix: "+",
    label: "Active Users",
  },
  {
    value: 42,
    suffix: "%",
    label: "Time Saved",
  },
]
