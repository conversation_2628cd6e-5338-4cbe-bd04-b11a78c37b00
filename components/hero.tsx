"use client"

import { motion } from 'framer-motion';
import { <PERSON><PERSON> } from "@/components/ui/button"
import { GradientCard } from "@/components/ui/gradient-card"
import Link from "next/link"
import { Suspense, lazy } from "react"
import { WebflowFadeIn, WebflowTextReveal, WebflowMagnetic, WebflowMorphBackground } from "@/components/animations/WebflowAnimations"

// Lazy load the Beams component to improve initial page load
const Beams = lazy(() => import("@/components/backgrounds/beams"))

// Fallback background for when <PERSON><PERSON> is loading or fails
const FallbackBackground = () => (
  <div className="absolute inset-0 bg-[#0a0a0a]">
    <div className="absolute inset-0 bg-gradient-to-br from-slate-950/50 via-blue-950/20 to-purple-950/20"></div>
    <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-500/10 via-transparent to-transparent"></div>
  </div>
)

export default function Hero() {
  return (
    <section className="relative py-20 sm:py-24 lg:py-32 overflow-hidden">
      {/* Webflow-style morphing background */}
      <WebflowMorphBackground className="opacity-50" />

      {/* Animated badge with enhanced Webflow styling */}
      <WebflowFadeIn delay={0.2}>
        <WebflowMagnetic strength={0.2}>
          <motion.div
            className="inline-flex items-center rounded-full bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 backdrop-blur-xl border border-blue-500/30 px-6 py-3 text-sm text-blue-300 mb-8"
            whileHover={{
              scale: 1.05,
              boxShadow: '0 8px 32px rgba(59, 130, 246, 0.3)',
              borderColor: 'rgba(59, 130, 246, 0.5)'
            }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <span className="mr-2">🚀</span>
            Validate and build your SaaS ideas efficiently
          </motion.div>
        </WebflowMagnetic>
      </WebflowFadeIn>

      {/* Main headline with enhanced Webflow-style animations */}
      <div className="max-w-4xl">
        <div className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight text-white mb-6 font-display">
          <WebflowFadeIn delay={0.4} direction="up">
            <motion.span
              className="block"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              SaaSifyx
            </motion.span>
          </WebflowFadeIn>
          <WebflowFadeIn delay={0.6} direction="up">
            <motion.span
              className="block gradient-text-hero"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              Ideas into Reality
            </motion.span>
          </WebflowFadeIn>
        </div>
        {/* Enhanced tagline with Webflow text reveal */}
        <WebflowFadeIn delay={0.8} direction="up">
          <div className="text-xl sm:text-2xl font-semibold text-slate-300 mb-8 font-display tracking-wide">
            <WebflowTextReveal delay={0.9}>
              Sketch it. SaaSify it. Xecute it.
            </WebflowTextReveal>
          </div>
        </WebflowFadeIn>

        <WebflowFadeIn delay={1.0} direction="up">
          <p className="text-lg sm:text-xl text-slate-300 mb-8 max-w-2xl leading-relaxed">
            AI-powered platform that analyzes, validates, and helps you build successful SaaS applications from concept to launch.
          </p>
        </WebflowFadeIn>

        {/* Enhanced CTA Buttons with Webflow styling */}
        <WebflowFadeIn delay={1.2} direction="up">
          <div className="flex flex-wrap gap-4">
            <WebflowMagnetic strength={0.15}>
              <motion.div
                whileHover={{
                  scale: 1.05,
                  boxShadow: '0 20px 40px rgba(96, 165, 250, 0.4)'
                }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                <Button
                  size="lg"
                  className="btn-webflow text-white px-8 py-6 rounded-xl font-medium text-lg relative overflow-hidden group"
                  asChild
                >
                  <Link href="/register">
                    <motion.span
                      className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5"
                      initial={{ x: '100%' }}
                      whileHover={{ x: '-100%' }}
                      transition={{ duration: 0.6, ease: [0.23, 1, 0.32, 1] }}
                    />
                    <span className="relative z-10">Get Started Free</span>
                  </Link>
                </Button>
              </motion.div>
            </WebflowMagnetic>

            <WebflowMagnetic strength={0.15}>
              <motion.div
                whileHover={{
                  scale: 1.05,
                  boxShadow: '0 20px 40px rgba(255, 255, 255, 0.1)'
                }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                <Button
                  variant="outline"
                  size="lg"
                  className="card-webflow border-white/20 text-slate-300 hover:text-white px-8 py-6 rounded-xl font-medium text-lg relative overflow-hidden group"
                  onClick={e => {
                    e.preventDefault();
                    const el = document.getElementById('features');
                    if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' });
                  }}
                >
                  <motion.span
                    className="absolute inset-0 bg-gradient-to-r from-white/5 to-white/10"
                    initial={{ x: '100%' }}
                    whileHover={{ x: '-100%' }}
                    transition={{ duration: 0.6, ease: [0.23, 1, 0.32, 1] }}
                  />
                  <span className="relative z-10">Explore Features</span>
                </Button>
              </motion.div>
            </WebflowMagnetic>
          </div>
        </WebflowFadeIn>

        {/* Market Feasibility Score Card */}
        <motion.div
          className="mt-12 bg-slate-900/50 backdrop-blur-lg rounded-2xl p-6 border border-slate-800 max-w-md"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          whileHover={{ 
            scale: 1.02,
            boxShadow: '0 0 20px rgba(59,130,246,0.2)'
          }}
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-slate-200">Market Feasibility</h3>
            <span className="text-2xl font-bold text-blue-400">8.2/10</span>
          </div>
          
          <div className="space-y-4">
            {[
              { label: 'Uniqueness', value: 85, color: 'emerald' },
              { label: 'Stickiness', value: 92, color: 'blue' },
              { label: 'Growth', value: 78, color: 'purple' },
              { label: 'Pricing', value: 88, color: 'teal' }
            ].map(({ label, value, color }) => (
              <div key={label} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">{label}</span>
                  <span className="text-slate-300">{value}%</span>
                </div>
                <motion.div 
                  className="h-1.5 rounded-full bg-slate-800"
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{ duration: 1, delay: 0.8 }}
                >
                  <motion.div
                    className={`h-full rounded-full bg-${color}-500`}
                    initial={{ width: 0 }}
                    animate={{ width: `${value}%` }}
                    transition={{ duration: 1, delay: 1 }}
                  />
                </motion.div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
