"use client"

import * as React from "react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import { Button } from "@/components/ui/button"
import { Menu } from "lucide-react"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

export default function Navbar() {
  const [visible, setVisible] = React.useState(true);
  const lastScrollY = React.useRef(0);
  const ticking = React.useRef(false);

  React.useEffect(() => {
    const handleScroll = () => {
      if (!ticking.current) {
        window.requestAnimationFrame(() => {
          const currentScrollY = window.scrollY;
          if (currentScrollY > lastScrollY.current && currentScrollY > 80) {
            setVisible(false); // scroll down, hide
          } else {
            setVisible(true); // scroll up, show
          }
          lastScrollY.current = currentScrollY;
          ticking.current = false;
        });
        ticking.current = true;
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Smooth scroll handler for in-page links
  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, id: string) => {
    e.preventDefault();
    const el = document.getElementById(id);
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <header
      className={
        `fixed top-0 z-50 w-full backdrop-blur-xl bg-black/20 border-b border-white/10 transition-transform duration-300 ${visible ? 'translate-y-0' : '-translate-y-full'}`
      }
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center gap-2">
            <Link href="/" className="flex items-center space-x-2">
              <span className="font-display font-bold tracking-wide text-xl text-white">SassifyX</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex lg:items-center lg:gap-6">
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="bg-transparent text-white hover:bg-white/10">
                    Features
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-2 bg-black/90 backdrop-blur-xl border border-white/10">
                      <ListItem href="#features" title="AI Analysis" icon="✨" className="nav-link-webflow">
                        Advanced AI-powered analysis of your SaaS idea
                      </ListItem>
                      <ListItem href="#features" title="User Flow" icon="🔄" className="nav-link-webflow">
                        Create sophisticated visual user flows
                      </ListItem>
                      <ListItem href="#features" title="Kanban Board" icon="📋" className="nav-link-webflow">
                        Smart project management with AI tickets
                      </ListItem>
                      <ListItem href="#features" title="Cursor AI" icon="🤖">
                        Premium AI coding assistance
                      </ListItem>
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <a
                    href="#pricing"
                    onClick={e => handleSmoothScroll(e, 'pricing')}
                    className={cn(navigationMenuTriggerStyle(), "nav-link-webflow bg-transparent text-white hover:bg-white/10 cursor-pointer")}
                  >
                    Pricing
                  </a>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <a
                    href="#about"
                    onClick={e => handleSmoothScroll(e, 'about')}
                    className={cn(navigationMenuTriggerStyle(), "nav-link-webflow bg-transparent text-white hover:bg-white/10 cursor-pointer")}
                  >
                    About
                  </a>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            <div className="flex items-center gap-3">
              <Button variant="ghost" asChild className="text-white hover:bg-white/10">
                <Link href="/auth/signin">Sign In</Link>
              </Button>
              <Button
                asChild
                className="btn-webflow text-white border-0 shadow-lg"
              >
                <Link href="/auth/signup">Get Started</Link>
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="flex lg:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="text-white hover:bg-white/10">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent
                side="right"
                className="w-[300px] sm:w-[400px] bg-black/90 backdrop-blur-xl border-white/10"
              >
                <nav className="flex flex-col gap-4">
                  <a
                    href="#features"
                    onClick={e => handleSmoothScroll(e, 'features')}
                    className="block px-2 py-1 text-lg font-medium text-white hover:text-blue-400 cursor-pointer"
                  >
                    Features
                  </a>
                  <a
                    href="#pricing"
                    onClick={e => handleSmoothScroll(e, 'pricing')}
                    className="block px-2 py-1 text-lg font-medium text-white hover:text-blue-400 cursor-pointer"
                  >
                    Pricing
                  </a>
                  <a
                    href="#about"
                    onClick={e => handleSmoothScroll(e, 'about')}
                    className="block px-2 py-1 text-lg font-medium text-white hover:text-blue-400 cursor-pointer"
                  >
                    About
                  </a>
                  <div className="flex flex-col gap-3 mt-6">
                    <Button
                      variant="outline"
                      asChild
                      className="w-full glass text-white border-white/20 hover:bg-white/10"
                    >
                      <Link href="/auth/signin">Sign In</Link>
                    </Button>
                    <Button
                      asChild
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
                    >
                      <Link href="/auth/signup">Get Started</Link>
                    </Button>
                  </div>
                </nav>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  )
}

const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a"> & { icon?: string; title: string }
>(({ className, title, icon, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-white/10 focus:bg-white/10 text-white",
            className,
          )}
          {...props}
        >
          <div className="flex items-center gap-2 text-sm font-medium leading-none">
            {icon && <span>{icon}</span>}
            {title}
          </div>
          <p className="line-clamp-2 text-sm leading-snug text-slate-300">{children}</p>
        </a>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem"
