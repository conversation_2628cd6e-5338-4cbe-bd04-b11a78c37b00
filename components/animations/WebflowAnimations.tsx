'use client';

import { motion, useInView, useScroll, useTransform, useSpring } from 'framer-motion';
import { useRef, useEffect, ReactNode } from 'react';

// Webflow-style fade in animation
interface WebflowFadeInProps {
  children: ReactNode;
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  className?: string;
}

export function WebflowFadeIn({ 
  children, 
  delay = 0, 
  duration = 0.8, 
  direction = 'up',
  className = '' 
}: WebflowFadeInProps) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-10%" });

  const getInitialTransform = () => {
    switch (direction) {
      case 'up': return { y: 60, opacity: 0 };
      case 'down': return { y: -60, opacity: 0 };
      case 'left': return { x: -60, opacity: 0 };
      case 'right': return { x: 60, opacity: 0 };
      default: return { y: 60, opacity: 0 };
    }
  };

  return (
    <motion.div
      ref={ref}
      initial={getInitialTransform()}
      animate={isInView ? { x: 0, y: 0, opacity: 1 } : getInitialTransform()}
      transition={{
        duration,
        delay,
        ease: [0.23, 1, 0.32, 1], // Webflow's signature easing
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Webflow-style stagger animation for lists
interface WebflowStaggerProps {
  children: ReactNode[];
  staggerDelay?: number;
  className?: string;
}

export function WebflowStagger({ children, staggerDelay = 0.1, className = '' }: WebflowStaggerProps) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-10%" });

  return (
    <div ref={ref} className={className}>
      {children.map((child, index) => (
        <motion.div
          key={index}
          initial={{ y: 60, opacity: 0 }}
          animate={isInView ? { y: 0, opacity: 1 } : { y: 60, opacity: 0 }}
          transition={{
            duration: 0.8,
            delay: index * staggerDelay,
            ease: [0.23, 1, 0.32, 1],
          }}
        >
          {child}
        </motion.div>
      ))}
    </div>
  );
}

// Webflow-style parallax scroll
interface WebflowParallaxProps {
  children: ReactNode;
  speed?: number;
  className?: string;
}

export function WebflowParallax({ children, speed = 0.5, className = '' }: WebflowParallaxProps) {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, speed * 100]);
  const springY = useSpring(y, { stiffness: 100, damping: 30 });

  return (
    <motion.div
      ref={ref}
      style={{ y: springY }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Webflow-style magnetic hover effect
interface WebflowMagneticProps {
  children: ReactNode;
  strength?: number;
  className?: string;
}

export function WebflowMagnetic({ children, strength = 0.3, className = '' }: WebflowMagneticProps) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const handleMouseMove = (e: MouseEvent) => {
      const rect = element.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;
      
      element.style.transform = `translate(${x * strength}px, ${y * strength}px)`;
    };

    const handleMouseLeave = () => {
      element.style.transform = 'translate(0px, 0px)';
    };

    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mousemove', handleMouseMove);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [strength]);

  return (
    <div
      ref={ref}
      className={`transition-transform duration-300 ease-out ${className}`}
      style={{ willChange: 'transform' }}
    >
      {children}
    </div>
  );
}

// Webflow-style scale on scroll
interface WebflowScaleScrollProps {
  children: ReactNode;
  scaleRange?: [number, number];
  className?: string;
}

export function WebflowScaleScroll({ 
  children, 
  scaleRange = [0.8, 1], 
  className = '' 
}: WebflowScaleScrollProps) {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [scaleRange[0], 1, scaleRange[1]]);
  const springScale = useSpring(scale, { stiffness: 100, damping: 30 });

  return (
    <motion.div
      ref={ref}
      style={{ scale: springScale }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

// Webflow-style text reveal
interface WebflowTextRevealProps {
  children: string;
  delay?: number;
  className?: string;
}

export function WebflowTextReveal({ children, delay = 0, className = '' }: WebflowTextRevealProps) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-10%" });

  const words = children.split(' ');

  return (
    <div ref={ref} className={className}>
      {words.map((word, index) => (
        <motion.span
          key={index}
          initial={{ y: 100, opacity: 0 }}
          animate={isInView ? { y: 0, opacity: 1 } : { y: 100, opacity: 0 }}
          transition={{
            duration: 0.8,
            delay: delay + index * 0.1,
            ease: [0.23, 1, 0.32, 1],
          }}
          className="inline-block mr-2"
          style={{ overflow: 'hidden' }}
        >
          {word}
        </motion.span>
      ))}
    </div>
  );
}

// Webflow-style counter animation
interface WebflowCounterProps {
  from: number;
  to: number;
  duration?: number;
  suffix?: string;
  className?: string;
}

export function WebflowCounter({ 
  from, 
  to, 
  duration = 2, 
  suffix = '', 
  className = '' 
}: WebflowCounterProps) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-10%" });
  
  const count = useTransform(
    useSpring(isInView ? to : from, { 
      duration: duration * 1000,
      bounce: 0 
    }),
    (value) => Math.round(value)
  );

  return (
    <motion.span ref={ref} className={className}>
      <motion.span>{count}</motion.span>
      {suffix}
    </motion.span>
  );
}

// Webflow-style morphing background
interface WebflowMorphBackgroundProps {
  className?: string;
}

export function WebflowMorphBackground({ className = '' }: WebflowMorphBackgroundProps) {
  return (
    <div className={`absolute inset-0 overflow-hidden ${className}`}>
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10"
        animate={{
          background: [
            'linear-gradient(45deg, rgba(59,130,246,0.1), rgba(139,92,246,0.1), rgba(236,72,153,0.1))',
            'linear-gradient(135deg, rgba(139,92,246,0.1), rgba(236,72,153,0.1), rgba(59,130,246,0.1))',
            'linear-gradient(225deg, rgba(236,72,153,0.1), rgba(59,130,246,0.1), rgba(139,92,246,0.1))',
            'linear-gradient(315deg, rgba(59,130,246,0.1), rgba(139,92,246,0.1), rgba(236,72,153,0.1))',
          ]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "linear"
        }}
      />
    </div>
  );
}
