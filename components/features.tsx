"use client"

import {
  <PERSON>rad<PERSON><PERSON><PERSON>,
  GradientCardContent,
  GradientCardDescription,
  GradientCardHeader,
  GradientCardTitle,
} from "@/components/ui/gradient-card"
import { motion, useMotionTemplate, useMotionValue, useTransform } from 'framer-motion';
import { <PERSON><PERSON>ir<PERSON>, BarChart3, Code, Workflow, Kanban } from "lucide-react"
import { useEffect, useRef } from 'react';
import { WebflowFadeIn, WebflowMagnetic } from "@/components/animations/WebflowAnimations"

interface FeaturesProps {
  feature: string;
  index: number;
  mousePosition: { x: number; y: number };
}

export default function Features({ feature, index, mousePosition }: FeaturesProps) {
  const cardRef = useRef<HTMLDivElement>(null);
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  // Calculate card tilt based on mouse position
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      if (!cardRef.current) return;
      const rect = cardRef.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      const rotateX = (event.clientY - centerY) / 20;
      const rotateY = (centerX - event.clientX) / 20;
      
      mouseX.set(rotateY);
      mouseY.set(rotateX);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [mouseX, mouseY]);

  const rotateX = useTransform(mouseY, [-15, 15], [10, -10]);
  const rotateY = useTransform(mouseX, [-15, 15], [-10, 10]);
  const perspective = 1000;

  const getIcon = (featureName: string) => {
    const baseClass = "transition-all duration-300 group-hover:scale-110";
    switch (featureName) {
      case 'AI Analysis':
        return <BarChart3 className={`${baseClass} text-blue-400 group-hover:text-blue-300`} />;
      case 'User Flow Designer':
        return <Workflow className={`${baseClass} text-purple-400 group-hover:text-purple-300`} />;
      case 'Smart Kanban Board':
        return <Kanban className={`${baseClass} text-emerald-400 group-hover:text-emerald-300`} />;
      case 'Cursor AI Integration':
        return <Code className={`${baseClass} text-amber-400 group-hover:text-amber-300`} />;
      default:
        return <CheckCircle className={`${baseClass} text-blue-400 group-hover:text-blue-300`} />;
    }
  };

  const getGradient = (featureName: string) => {
    switch (featureName) {
      case 'AI Analysis':
        return 'from-blue-500/20 via-indigo-500/20 to-violet-500/20';
      case 'User Flow Designer':
        return 'from-purple-500/20 via-fuchsia-500/20 to-pink-500/20';
      case 'Smart Kanban Board':
        return 'from-emerald-500/20 via-teal-500/20 to-cyan-500/20';
      case 'Cursor AI Integration':
        return 'from-amber-500/20 via-orange-500/20 to-red-500/20';
      default:
        return 'from-blue-500/20 via-indigo-500/20 to-violet-500/20';
    }
  };

  const getDescription = (featureName: string) => {
    switch (featureName) {
      case 'AI Analysis':
        return "Advanced AI-powered analysis of your SaaS idea based on six core market pillars";
      case 'User Flow Designer':
        return "Create sophisticated visual user flows with our premium diagram editor";
      case 'Smart Kanban Board':
        return "Intelligent project management with AI-generated tickets";
      case 'Cursor AI Integration':
        return "Advanced coding assistance integrated directly into your workflow";
      default:
        return "Premium feature description";
    }
  };

  return (
    <WebflowFadeIn delay={index * 0.15} direction="up">
      <WebflowMagnetic strength={0.1}>
        <motion.div
          ref={cardRef}
          className="relative group perspective-1000"
          style={{
            perspective,
            transformStyle: 'preserve-3d',
            rotateX,
            rotateY,
          }}
          whileHover={{
            scale: 1.05,
            boxShadow: '0 25px 50px rgba(59, 130, 246, 0.15)',
            transition: { type: "spring", stiffness: 300, damping: 20 }
          }}
        >
          <GradientCard className="card-webflow relative overflow-hidden transition-all duration-500">
        {/* Animated gradient background */}
        <motion.div
          className={`absolute inset-0 bg-gradient-to-br ${getGradient(feature)} opacity-50`}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.5, scale: 1 }}
          transition={{ duration: 0.5 }}
          style={{
            filter: 'blur(20px)',
            transform: 'translate3d(0, 0, -20px)',
          }}
        />

        {/* Interactive light effect */}
        <motion.div
          className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          style={{
            background: useMotionTemplate`
              radial-gradient(
                400px circle at ${mousePosition.x * 100}% ${mousePosition.y * 100}%,
                rgba(255,255,255,0.1),
                transparent 80%
              )
            `,
          }}
        />

        <GradientCardHeader className="relative space-y-1 pb-4">
          <motion.div 
            className="flex items-center space-x-3"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ 
              duration: 0.5,
              delay: index * 0.1,
              type: "spring",
              stiffness: 200,
              damping: 20
            }}
          >
            <div className="p-2 rounded-lg bg-gradient-to-br from-white/5 to-white/10">
              {getIcon(feature)}
            </div>
            <GradientCardTitle className="text-lg sm:text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-white to-white/80">
              {feature}
            </GradientCardTitle>
          </motion.div>
        </GradientCardHeader>

        <GradientCardContent className="relative">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ 
              duration: 0.5,
              delay: index * 0.1 + 0.1,
              type: "spring",
              stiffness: 200,
              damping: 20
            }}
          >
            <GradientCardDescription className="text-sm sm:text-base text-slate-300/90">
              {getDescription(feature)}
            </GradientCardDescription>
          </motion.div>
        </GradientCardContent>

        {/* Border glow effect */}
        <motion.div
          className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          style={{
            background: `linear-gradient(to right, transparent, ${feature === 'AI Analysis' ? 'rgba(59,130,246,0.1)' : 
              feature === 'User Flow Designer' ? 'rgba(168,85,247,0.1)' : 
              feature === 'Smart Kanban Board' ? 'rgba(16,185,129,0.1)' : 
              'rgba(245,158,11,0.1)'}, transparent)`,
            filter: 'blur(4px)',
            transform: 'translate3d(0, 0, -10px)',
          }}
        />
          </GradientCard>
        </motion.div>
      </WebflowMagnetic>
    </WebflowFadeIn>
  );
}
