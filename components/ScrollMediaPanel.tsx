'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Play, Pause } from 'lucide-react';

interface MediaItem {
  id: string;
  type: 'video' | 'image';
  src: string;
  title: string;
  description: string;
}

interface ScrollMediaPanelProps {
  isVisible: boolean;
  onClose: () => void;
  currentSection: string;
}

const mediaContent: Record<string, MediaItem> = {
  hero: {
    id: 'hero',
    type: 'video',
    src: '/videos/hero-demo.mp4',
    title: 'SaaSifyx Platform Overview',
    description: 'See how SaaSifyx transforms your ideas into reality with AI-powered analysis and development tools.'
  },
  features: {
    id: 'features',
    type: 'video',
    src: '/videos/features-demo.mp4',
    title: 'AI Analysis in Action',
    description: 'Watch our AI analyze your SaaS idea across six core pillars and generate actionable insights.'
  },
  statistics: {
    id: 'statistics',
    type: 'image',
    src: '/images/statistics-showcase.jpg',
    title: 'Proven Results',
    description: 'Join thousands of successful developers building profitable SaaS applications.'
  },
  about: {
    id: 'about',
    type: 'video',
    src: '/videos/about-mission.mp4',
    title: 'Our Mission',
    description: 'Learn about SaaSifyx\'s mission to make SaaS innovation accessible to everyone.'
  },
  cta: {
    id: 'cta',
    type: 'image',
    src: '/images/success-stories.jpg',
    title: 'Success Stories',
    description: 'Discover how entrepreneurs transformed their ideas into successful SaaS businesses.'
  }
};

export default function ScrollMediaPanel({ isVisible, onClose, currentSection }: ScrollMediaPanelProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentMedia, setCurrentMedia] = useState<MediaItem | null>(null);

  useEffect(() => {
    const media = mediaContent[currentSection];
    if (media) {
      setCurrentMedia(media);
      setIsPlaying(false);
    }
  }, [currentSection]);

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  if (!isVisible || !currentMedia) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ x: '100%', opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: '100%', opacity: 0 }}
        transition={{
          type: 'spring',
          stiffness: 300,
          damping: 30,
          duration: 0.5
        }}
        className="fixed right-4 top-1/2 -translate-y-1/2 w-72 h-80 bg-black/95 backdrop-blur-2xl border border-white/10 rounded-2xl z-30 flex flex-col shadow-2xl"
      >
        {/* Compact Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <div>
            <h3 className="text-sm font-semibold text-white">{currentMedia.title}</h3>
            <p className="text-xs text-slate-400 mt-1">Section: {currentSection}</p>
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded-lg bg-white/5 hover:bg-white/10 transition-colors"
          >
            <X className="w-4 h-4 text-white" />
          </button>
        </div>

        {/* Media Content */}
        <div className="flex-1 p-3">
          <div className="relative rounded-lg overflow-hidden bg-slate-900/50 h-full">
            <video
              ref={videoRef}
              src={currentMedia.videoUrl}
              className="w-full h-full object-cover"
              muted
              loop
              playsInline
              autoPlay
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
            <div className="absolute bottom-2 left-2 flex items-center space-x-1">
              <div className="w-1.5 h-1.5 bg-red-500 rounded-full animate-pulse" />
              <span className="text-xs text-white font-medium">Live</span>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
