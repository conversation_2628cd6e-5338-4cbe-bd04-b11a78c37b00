'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Play, Pause } from 'lucide-react';

interface MediaItem {
  id: string;
  type: 'video' | 'image';
  src: string;
  title: string;
  description: string;
}

interface ScrollMediaPanelProps {
  isVisible: boolean;
  onClose: () => void;
  currentSection: string;
}

const mediaContent: Record<string, MediaItem> = {
  hero: {
    id: 'hero',
    type: 'video',
    src: '/videos/hero-demo.mp4',
    title: 'SaaSifyx Platform Overview',
    description: 'See how SaaSifyx transforms your ideas into reality with AI-powered analysis and development tools.'
  },
  features: {
    id: 'features',
    type: 'video',
    src: '/videos/features-demo.mp4',
    title: 'AI Analysis in Action',
    description: 'Watch our AI analyze your SaaS idea across six core pillars and generate actionable insights.'
  },
  statistics: {
    id: 'statistics',
    type: 'image',
    src: '/images/statistics-showcase.jpg',
    title: 'Proven Results',
    description: 'Join thousands of successful developers building profitable SaaS applications.'
  },
  about: {
    id: 'about',
    type: 'video',
    src: '/videos/about-mission.mp4',
    title: 'Our Mission',
    description: 'Learn about SaaSifyx\'s mission to make SaaS innovation accessible to everyone.'
  },
  cta: {
    id: 'cta',
    type: 'image',
    src: '/images/success-stories.jpg',
    title: 'Success Stories',
    description: 'Discover how entrepreneurs transformed their ideas into successful SaaS businesses.'
  }
};

export default function ScrollMediaPanel({ isVisible, onClose, currentSection }: ScrollMediaPanelProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentMedia, setCurrentMedia] = useState<MediaItem | null>(null);

  useEffect(() => {
    const media = mediaContent[currentSection];
    if (media) {
      setCurrentMedia(media);
      setIsPlaying(false);
    }
  }, [currentSection]);

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  if (!isVisible || !currentMedia) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ x: '100%', opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: '100%', opacity: 0 }}
        transition={{ 
          type: 'spring',
          stiffness: 300,
          damping: 30,
          duration: 0.5
        }}
        className="fixed right-0 top-0 w-2/5 h-full bg-black/90 backdrop-blur-2xl border-l border-white/10 z-50 flex flex-col"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <div>
            <h3 className="text-lg font-semibold text-white">{currentMedia.title}</h3>
            <p className="text-sm text-slate-400 mt-1">Interactive Demo</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors"
          >
            <X className="w-5 h-5 text-white" />
          </button>
        </div>

        {/* Media Content */}
        <div className="flex-1 flex flex-col justify-center p-6">
          <div className="relative rounded-xl overflow-hidden bg-slate-900/50 aspect-video mb-6">
            {currentMedia.type === 'video' ? (
              <div className="relative w-full h-full flex items-center justify-center">
                {/* Video placeholder with play button */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                  <motion.button
                    onClick={togglePlay}
                    className="w-16 h-16 rounded-full bg-white/10 backdrop-blur-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {isPlaying ? (
                      <Pause className="w-6 h-6 text-white" />
                    ) : (
                      <Play className="w-6 h-6 text-white ml-1" />
                    )}
                  </motion.button>
                </div>
                
                {/* Video overlay with demo content */}
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="bg-black/50 backdrop-blur-xl rounded-lg p-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-xs text-white">Live Demo</span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-slate-800 to-slate-900 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center">
                    <span className="text-2xl">📊</span>
                  </div>
                  <p className="text-white font-medium">Interactive Showcase</p>
                </div>
              </div>
            )}
          </div>

          {/* Description */}
          <div className="space-y-4">
            <p className="text-slate-300 leading-relaxed">
              {currentMedia.description}
            </p>
            
            {/* Interactive elements */}
            <div className="grid grid-cols-2 gap-3">
              <motion.button
                className="p-3 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm font-medium hover:from-blue-700 hover:to-purple-700 transition-all"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Try Demo
              </motion.button>
              <motion.button
                className="p-3 rounded-lg bg-white/5 border border-white/10 text-white text-sm font-medium hover:bg-white/10 transition-all"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Learn More
              </motion.button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10">
          <div className="flex items-center justify-between text-sm text-slate-400">
            <span>Section: {currentSection}</span>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Interactive</span>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
