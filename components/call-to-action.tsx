"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import ScrollFloat from "@/components/animations/scroll-float"
import Link from "next/link"
import { WebflowFadeIn, WebflowMagnetic } from "@/components/animations/WebflowAnimations"
import { motion } from "framer-motion"

export default function CallToAction() {
  return (
    <section className="relative py-24 lg:py-32">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-500/5 to-transparent" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(139,92,246,0.08)_0%,transparent_70%)]" />

      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <ScrollFloat direction="up" className="max-w-4xl mx-auto">
          {/* macOS/iOS Style Card */}
          <div className="relative group">
            {/* Card Background with macOS styling */}
            <div className="relative bg-white/[0.03] backdrop-blur-3xl rounded-3xl border border-white/[0.08] shadow-2xl overflow-hidden">
              {/* Subtle gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/[0.05] via-transparent to-black/[0.02]" />

              {/* Content */}
              <div className="relative p-8 sm:p-12 lg:p-16">
                {/* Header Badge */}
                <div className="flex justify-center mb-8">
                  <div className="inline-flex items-center rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-xl border border-white/10 px-4 py-2 text-sm text-blue-300">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse" />
                    Ready to Launch?
                  </div>
                </div>

                {/* Main Content */}
                <div className="text-center space-y-8">
                  {/* Title */}
                  <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight leading-tight text-white">
                    Ready to transform your{" "}
                    <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-emerald-400 bg-clip-text text-transparent">
                      SaaS idea
                    </span>{" "}
                    into reality?
                  </h2>

                  {/* Description */}
                  <p className="mx-auto max-w-3xl text-xl md:text-2xl text-slate-300 leading-relaxed font-light">
                    Join thousands of entrepreneurs who've transformed their ideas into successful SaaS businesses with
                    our AI-powered platform.
                  </p>

                  {/* Enhanced Action Buttons with Webflow styling */}
                  <WebflowFadeIn delay={0.6} direction="up">
                    <div className="flex flex-col sm:flex-row gap-4 justify-center pt-8">
                      {/* Primary Button - Webflow style */}
                      <WebflowMagnetic strength={0.15}>
                        <motion.div
                          whileHover={{
                            scale: 1.05,
                            boxShadow: '0 25px 50px rgba(96, 165, 250, 0.4)'
                          }}
                          whileTap={{ scale: 0.95 }}
                          transition={{ type: "spring", stiffness: 300, damping: 20 }}
                        >
                          <Button
                            size="lg"
                            asChild
                            className="btn-webflow group relative w-full sm:w-auto text-white border-0 rounded-2xl shadow-lg text-lg px-8 py-4 font-medium"
                          >
                            <Link href="/register" className="flex items-center justify-center">
                              <span className="mr-2">🚀</span>
                              Start Free Trial
                              <motion.div
                                className="absolute inset-0 rounded-2xl bg-white/10 opacity-0 group-hover:opacity-100"
                                transition={{ duration: 0.3 }}
                              />
                            </Link>
                          </Button>
                        </motion.div>
                      </WebflowMagnetic>

                      {/* Secondary Button - Enhanced Webflow style */}
                      <WebflowMagnetic strength={0.15}>
                        <motion.div
                          whileHover={{
                            scale: 1.05,
                            boxShadow: '0 25px 50px rgba(255, 255, 255, 0.1)'
                          }}
                          whileTap={{ scale: 0.95 }}
                          transition={{ type: "spring", stiffness: 300, damping: 20 }}
                        >
                          <Button
                            size="lg"
                            variant="outline"
                            asChild
                            className="card-webflow group relative w-full sm:w-auto text-white border-white/20 hover:border-white/40 rounded-2xl shadow-lg text-lg px-8 py-4 font-medium"
                          >
                            <Link href="/contact" className="flex items-center justify-center">
                              <span className="mr-2">📅</span>
                              Schedule Demo
                              <motion.div
                                className="absolute inset-0 rounded-2xl bg-white/5 opacity-0 group-hover:opacity-100"
                                transition={{ duration: 0.3 }}
                              />
                            </Link>
                          </Button>
                        </motion.div>
                      </WebflowMagnetic>
                    </div>
                  </WebflowFadeIn>

                  {/* Trust Indicators */}
                  <div className="pt-8 space-y-4">
                    <div className="flex items-center justify-center space-x-6 text-sm text-slate-400">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-400 rounded-full mr-2" />
                        No credit card required
                      </div>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
                        14-day free trial
                      </div>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-purple-400 rounded-full mr-2" />
                        Cancel anytime
                      </div>
                    </div>

                    {/* Social Proof */}
                    <div className="flex items-center justify-center space-x-2 text-sm text-slate-500">
                      <div className="flex -space-x-2">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full border-2 border-white/20" />
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full border-2 border-white/20" />
                        <div className="w-8 h-8 bg-gradient-to-r from-emerald-400 to-blue-400 rounded-full border-2 border-white/20" />
                        <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-red-400 rounded-full border-2 border-white/20" />
                      </div>
                      <span>Join 2,500+ entrepreneurs</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Bottom highlight line - iOS style */}
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1/3 h-1 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full" />
            </div>

            {/* Outer glow effect */}
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-emerald-500/10 blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500 -z-10" />
          </div>
        </ScrollFloat>
      </div>
    </section>
  )
}
